from scipy.optimize import linprog

def dietu_problem(cost, f_min, f_max, n_min, n_max, amt):
    n_foods = len(cost)
    
    if len(f_min) != n_foods or len(f_max) != n_foods:
        raise ValueError("Length of f_min and f_max must match the number of foods")
    
    for j in range(n_foods):
        if f_min[j] < 0:
            raise ValueError("Food minimum amounts must be non-negative")
        if f_min[j] > f_max[j]:
            raise ValueError("Food minimum amount cannot exceed maximum amount")
    
    M = len(n_min)
    N = len(n_max)
    total_nutrients = M + N
    
    if total_nutrients > 0:
        if len(amt) != total_nutrients:
            raise ValueError(f"amt must have exactly {total_nutrients} rows (M+N), but got {len(amt)}")
        for i in range(total_nutrients):
            if len(amt[i]) != n_foods:
                raise ValueError(f"Row {i} of amt has {len(amt[i])} elements, expected {n_foods} (number of foods)")
    
    for i in range(M):
        if n_min[i] < 0:
            raise ValueError("Minimum nutrient requirements must be non-negative")
    for i in range(N):
        if n_max[i] < 0:
            raise ValueError("Maximum nutrient requirements must be non-negative")
    
    for i in range(total_nutrients):
        for j in range(n_foods):
            if amt[i][j] < 0:
                raise ValueError("Nutrient amounts must be non-negative")
    
    if total_nutrients == 0:
        A_ub = None
        b_ub = None
    else:
        A_ub = []
        b_ub = []
        for i in range(M):
            A_ub.append([-x for x in amt[i]])
            b_ub.append(-n_min[i])
        for i in range(M, total_nutrients):
            A_ub.append(amt[i])
            b_ub.append(n_max[i - M])
    
    bounds = [(f_min[j], f_max[j]) for j in range(n_foods)]
    
    res = linprog(cost, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')
    
    if res.success:
        return res.fun
    else:
        return None