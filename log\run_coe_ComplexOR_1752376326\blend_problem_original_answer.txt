To solve this problem, we need to determine the optimal amounts of various alloys to purchase such that the final blend meets the desired percentage composition of required elements at the minimum cost. The problem is formulated as a linear programming (LP) model where the objective is to minimize the total cost subject to constraints on the element composition and the total amount of the blend.

### Approach
1. **Problem Analysis**: The problem involves blending different alloys to achieve a desired composition of elements. Each alloy has a specific percentage composition of elements and a price. The goal is to find the amounts of each alloy to purchase such that:
   - The weighted average of each element in the blend matches the desired percentage.
   - The total amount of the blend is exactly 1 unit.
   - The total cost is minimized.

2. **Formulation as LP**: The problem can be modeled as an LP with the following components:
   - **Decision Variables**: The amount of each alloy to purchase (non-negative).
   - **Objective Function**: Minimize the total cost, which is the sum of the products of the amounts purchased and their prices.
   - **Constraints**:
     - **Element Composition Constraints**: For each required element, the weighted sum of its percentages from all alloys must equal the desired percentage.
     - **Total Blend Constraint**: The sum of all purchased amounts must equal 1 unit.

3. **Input Validation**: Ensure that all inputs are consistent and non-negative. This includes checking the dimensions of the composition data and verifying that prices, compositions, and desired percentages are non-negative.

4. **Edge Case Handling**:
   - If no alloys are available, the problem is infeasible (return infinity).
   - If no elements are required, only the total blend constraint is active, and the solution minimizes cost subject to the total amount being 1.

5. **Solver Selection**: Use the `linprog` function from SciPy's `optimize` module with the 'highs' solver, which is efficient for LP problems. The solver is configured to handle equality constraints and non-negativity bounds.

### Solution Code
```python
import numpy as np
from scipy.optimize import linprog

def blend_problem(alloys_on_market, required_elements, composition_data, desired_blend_percentage, alloy_price):
    n = len(required_elements)
    m = len(alloys_on_market)
    
    if len(desired_blend_percentage) != n:
        raise ValueError("desired_blend_percentage must have the same length as required_elements")
    if len(alloy_price) != m:
        raise ValueError("alloy_price must have the same length as alloys_on_market")
    if len(composition_data) != n:
        raise ValueError("composition_data must have the same number of rows as required_elements")
    for i in range(n):
        if len(composition_data[i]) != m:
            raise ValueError(f"Row {i} of composition_data has length {len(composition_data[i])}, expected {m}")

    if any(p < 0 for p in alloy_price):
        raise ValueError("Alloy prices must be non-negative")
    for i in range(n):
        if any(comp < 0 for comp in composition_data[i]):
            raise ValueError(f"Composition percentages in row {i} must be non-negative")
    if any(d < 0 for d in desired_blend_percentage):
        raise ValueError("Desired blend percentages must be non-negative")

    if m == 0:
        return float('inf')

    comp_array = np.array(composition_data, dtype=float)
    A_eq = np.vstack((comp_array, np.ones(m)))
    b_eq = np.append(desired_blend_percentage, 1.0)
    bounds = [(0, None)] * m

    try:
        res = linprog(c=alloy_price, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method='highs')
    except:
        return float('inf')
    
    if res.success:
        return res.fun
    else:
        return float('inf')
```

### Explanation
1. **Input Validation**: The function first checks if the input dimensions are consistent (e.g., the number of desired percentages matches the number of required elements). It also ensures all prices, compositions, and desired percentages are non-negative.
2. **Edge Case Handling**: If no alloys are available, the problem is immediately deemed infeasible, and infinity is returned.
3. **Constraint Setup**: The composition data and the total blend constraint (sum of amounts equals 1) are combined into a matrix `A_eq` and a vector `b_eq` for the equality constraints.
4. **Solver Invocation**: The `linprog` function is called with the objective to minimize the total cost, subject to the equality constraints and non-negativity bounds on the decision variables.
5. **Result Handling**: If the solver finds an optimal solution, the minimum cost is returned. If the problem is infeasible or the solver encounters an error, infinity is returned.

This approach efficiently formulates and solves the alloy blending problem using linear programming, ensuring optimality and correctness while handling edge cases and input validation robustly.