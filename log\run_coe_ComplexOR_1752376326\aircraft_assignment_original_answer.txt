### Final Solution Code
After synthesizing all expert insights, here's the optimized implementation for the Aircraft Assignment Problem:

```python
import gurobipy as gp
from gurobipy import GRB

def aircraft_assignment(availability, demand, capabilities, costs, verbose=False):
    """
    Solves the Aircraft Assignment Problem to minimize total cost while satisfying:
    1) Aircraft availability constraints
    2) Route demand requirements
    
    Args:
        availability (list[int]): Available count per aircraft type
        demand (list[int]): Passenger demand per route
        capabilities (list[list[int]]): capabilities[i][j] = capacity of aircraft i on route j
        costs (list[list[float]]): costs[i][j] = cost of assigning aircraft i to route j
        verbose (bool): Enable solver output if True
        
    Returns:
        tuple: (total_cost, assignment_dict) if optimal solution found
        
    Raises:
        ValueError: For invalid input dimensions
        RuntimeError: For optimization failures
    """
    # Validate input dimensions
    n = len(availability)
    m = len(demand)
    
    if n == 0 or m == 0:
        raise ValueError("Must have at least one aircraft type and one route")
    
    if len(capabilities) != n or any(len(row) != m for row in capabilities):
        raise ValueError("capabilities must be n x m matrix")
    
    if len(costs) != n or any(len(row) != m for row in costs):
        raise ValueError("costs must be n x m matrix")
    
    # Create environment with controlled verbosity
    with gp.Env(params={"OutputFlag": 1 if verbose else 0}) as env:
        model = gp.Model("AircraftAssignment", env=env)
        
        # Create decision variables
        x = model.addVars(n, m, vtype=GRB.INTEGER, name="x")
        
        # Set objective: minimize total cost
        model.setObjective(
            gp.quicksum(costs[i][j] * x[i, j] for i in range(n) for j in range(m)),
            GRB.MINIMIZE
        )
        
        # Aircraft availability constraints
        for i in range(n):
            model.addConstr(
                gp.quicksum(x[i, j] for j in range(m)) <= availability[i],
                name=f"avail_{i}"
            )
        
        # Route demand constraints
        for j in range(m):
            model.addConstr(
                gp.quicksum(capabilities[i][j] * x[i, j] for i in range(n)) >= demand[j],
                name=f"demand_{j}"
            )
        
        # Solve the model
        model.optimize()
        
        # Process solution
        if model.status == GRB.OPTIMAL:
            total_cost = model.objVal
            assignment = {
                (i, j): int(x[i, j].X)
                for i in range(n)
                for j in range(m)
            }
            return total_cost, assignment
        
        # Handle non-optimal status
        status_map = {
            GRB.INFEASIBLE: "Model is infeasible. Check constraints.",
            GRB.UNBOUNDED: "Model is unbounded. Check objective.",
            GRB.INF_OR_UNBD: "Model is infeasible or unbounded",
            GRB.TIME_LIMIT: "Time limit reached. Solution may not be optimal",
            GRB.INTERRUPTED: "Optimization interrupted"
        }
        raise RuntimeError(status_map.get(model.status, 
                          f"Optimization failed with status {model.status}"))
```

### Key Features:
1. **Robust Input Validation**: Verifies matrix dimensions and data consistency
2. **Resource Management**: Uses context manager for automatic cleanup
3. **Verbosity Control**: Suppresses solver output by default
4. **Comprehensive Output**: Returns both total cost and detailed assignment plan
5. **Error Diagnostics**: Provides clear error messages for infeasible/unbounded cases
6. **Production-Ready**: Handles edge cases and converts solutions to integers

### Usage Example:
```python
# Define problem parameters
availability = [5, 8]  # 2 aircraft types
demand = [90, 120]     # 2 routes
capabilities = [[20, 30], [25, 35]]  # Aircraft capacities
costs = [[5000, 7000], [6000, 8000]]  # Assignment costs

# Solve with verbose output
total_cost, assignments = aircraft_assignment(
    availability,
    demand,
    capabilities,
    costs,
    verbose=True
)

# Print results
print(f"Optimal Total Cost: ${total_cost}")
for (ac, route), count in assignments.items():
    if count > 0:
        print(f"Aircraft {ac} → Route {route}: {count} units")
```

This implementation efficiently solves the aircraft assignment problem using integer linear programming while ensuring robustness and usability in real-world scenarios.