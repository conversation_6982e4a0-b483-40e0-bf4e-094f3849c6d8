import re
import json
import os
from langchain.chat_models import ChatOpenAI


def safe_json_parse(text, fallback_value=None):
    """
    Safely parse JSON text with fallback handling
    
    Args:
        text: The text to parse as JSON
        fallback_value: Value to return if parsing fails
        
    Returns:
        Parsed JSON object or fallback value
    """
    try:
        return json.loads(text)
    except (json.JSONDecodeError, TypeError, ValueError) as e:
        print(f"JSON parsing failed: {e}")
        print(f"Raw text: {text}")
        return fallback_value


def create_chat_openai(model_name, temperature=0, **kwargs):
    """Create ChatOpenAI instance with DeepSeek configuration if needed"""
    # Handle None model_name
    if model_name is None:
        model_name = 'deepseek-reasoner'
    
    if 'deepseek' in model_name.lower():
        # DeepSeek API configuration
        return ChatOpenAI(
            model_name=model_name,
            temperature=temperature,
            openai_api_key="sk-1db92669ba5e41038390cc497bc7f647",
            openai_api_base="https://api.deepseek.com/v1",
            **kwargs
        )
    else:
        # Default OpenAI configuration
        return ChatOpenAI(
            model_name=model_name,
            temperature=temperature,
            **kwargs
        )


def extract_code_from_string(input_string):
    # Match code within ```python ... ``` or ``` ... ``` blocks
    pattern = r'```(?:python)?\s*(.*?)\s*```'
    
    # Find all matches in the input string
    code_blocks = re.findall(pattern, input_string, re.DOTALL)

    if len(code_blocks) == 0:
        # print(f'Parse code error! {input_string}')
        return input_string
    elif len(code_blocks) == 1:
        return code_blocks[0]

    code_blocks = [code for code in code_blocks if 'pip' not in code]
    return '\n'.join(code_blocks)


def read_problem(dataset, problem_name):
    base_dir = 'dataset'
    with open(os.path.join(base_dir, dataset, problem_name, 'description.txt'), 'r', encoding='utf8') as f:
        description = f.read()

    with open(os.path.join(base_dir, dataset, problem_name, 'code_example.py'), 'r', encoding='utf8') as f:
        code_example = f.read()

    return {
        'description': description,
        'code_example': code_example
    }