To solve the Aircraft Landing Problem (ALP), we need to schedule aircraft landings such that each aircraft lands within its predetermined time window, maintains safe separation times between successive landings, and minimizes penalties for deviations from target landing times. The solution involves formulating the problem as a linear programming (LP) model.

### Approach
1. **Problem Analysis**: The problem requires assigning landing times to aircraft while respecting:
   - **Time Windows**: Each aircraft must land between its earliest and latest times.
   - **Separation Constraints**: Successive aircraft must maintain minimum time separations.
   - **Penalty Minimization**: Deviations from target landing times incur penalties (earliness or tardiness).

2. **LP Formulation**:
   - **Variables**:
     - `t_i`: Landing time of aircraft `i`.
     - `alpha_i`: Earliness (non-negative, `T_i - t_i` if early).
     - `beta_i`: Tardiness (non-negative, `t_i - T_i` if late).
   - **Constraints**:
     - Time window: `E_i ≤ t_i ≤ L_i`.
     - Target time: `t_i + alpha_i - beta_i = T_i`.
     - Separation: `t_j ≥ t_i + s_{ij}` for all `i < j`.
   - **Objective**: Minimize total penalty: `Σ (g_i * alpha_i + h_i * beta_i)`.

3. **Implementation**:
   - Use PuLP to model the LP problem.
   - Define variables, constraints, and objective.
   - Solve using CBC solver.
   - Return total penalty (rounded integer) or -1 if infeasible.

### Solution Code
```python
from pulp import *

def aircraft_landing(EarliestLanding, LatestLanding, TargetLanding, PenaltyAfterTarget, PenaltyBeforeTarget, SeparationTime):
    n = len(EarliestLanding)
    
    # Input validation
    for i in range(n):
        E = EarliestLanding[i]
        L = LatestLanding[i]
        T = TargetLanding[i]
        if not (E <= T <= L):
            return -1
        if PenaltyBeforeTarget[i] < 0 or PenaltyAfterTarget[i] < 0:
            return -1
    
    for i in range(n):
        for j in range(n):
            if SeparationTime[i][j] < 0:
                return -1
                
    prob = LpProblem("Aircraft_Landing_Problem", LpMinimize)
    
    t = [LpVariable(f"t_{i}", lowBound=EarliestLanding[i], upBound=LatestLanding[i]) for i in range(n)]
    alpha = [LpVariable(f"alpha_{i}", lowBound=0) for i in range(n)]
    beta = [LpVariable(f"beta_{i}", lowBound=0) for i in range(n)]
    
    total_penalty = lpSum([PenaltyBeforeTarget[i] * alpha[i] + PenaltyAfterTarget[i] * beta[i] for i in range(n)])
    prob += total_penalty
    
    for i in range(n):
        prob += t[i] + alpha[i] - beta[i] == TargetLanding[i], f"TargetTime_{i}"
    
    for i in range(n):
        for j in range(i + 1, n):
            prob += t[j] >= t[i] + SeparationTime[i][j], f"Separation_{i}_{j}"
    
    status = prob.solve(PULP_CBC_CMD(msg=False))
    
    if LpStatus[status] == "Optimal":
        total_penalty_value = value(total_penalty)
        return int(round(total_penalty_value))
    else:
        return -1
```

### Explanation
1. **Problem Setup**: The LP problem is initialized with `LpProblem`.
2. **Variables**:
   - `t[i]`: Landing time for aircraft `i`, bounded by `[E_i, L_i]`.
   - `alpha[i]` and `beta[i]`: Non-negative variables for earliness and tardiness.
3. **Objective**: Minimize total penalty: `Σ (g_i * alpha_i + h_i * beta_i)`.
4. **Constraints**:
   - **Target Time**: Ensures `t_i + alpha_i - beta_i = T_i`.
   - **Separation**: Ensures `t_j ≥ t_i + s_{ij}` for `i < j`.
5. **Solution Handling**: Solves using CBC. If optimal, returns rounded total penalty; otherwise, returns -1 for infeasibility.

This approach efficiently models ALP as an LP problem, leveraging PuLP to find optimal landing schedules while respecting all constraints and minimizing penalties. Input validation ensures robustness against invalid inputs, treating them as infeasible solutions.