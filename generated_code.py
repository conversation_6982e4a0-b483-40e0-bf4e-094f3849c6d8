from mip import Model, BINARY, CONTINUOUS, minimize, xsum

def flowshop_scheduling(jobs, schedules, machines, proces_time):
    n = len(jobs)
    M = len(machines)
    
    if n == 0 or M == 0:
        return 0
    
    model = Model(sense=minimize)
    
    x = [[model.add_var(var_type=BINARY) for k in range(n)] for j in range(n)]
    C = [[model.add_var(var_type=CONTINUOUS, lb=0.0) for m in range(M)] for k in range(n)]
    
    for j in range(n):
        model += xsum(x[j][k] for k in range(n)) == 1
        
    for k in range(n):
        model += xsum(x[j][k] for j in range(n)) == 1
        
    model += C[0][0] >= xsum(x[j][0] * proces_time[j][0] for j in range(n))
    
    for m in range(1, M):
        model += C[0][m] >= C[0][m-1] + xsum(x[j][0] * proces_time[j][m] for j in range(n))
        
    for k in range(1, n):
        model += C[k][0] >= C[k-1][0] + xsum(x[j][k] * proces_time[j][0] for j in range(n))
        
    for k in range(1, n):
        for m in range(1, M):
            model += C[k][m] >= C[k-1][m] + xsum(x[j][k] * proces_time[j][m] for j in range(n))
            model += C[k][m] >= C[k][m-1] + xsum(x[j][k] * proces_time[j][m] for j in range(n))
            
    model.objective = C[n-1][M-1]
    
    model.optimize()
    
    if model.num_solutions:
        makespan = round(C[n-1][M-1].x)
    else:
        makespan = 0
        
    return makespan