To solve the diet optimization problem, we need to minimize the total cost of purchasing foods while meeting specific nutritional constraints and adhering to purchase limits for each food. This is a classic linear programming (LP) problem where the goal is to find the optimal quantities of foods to purchase such that the total nutrient intake meets minimum and maximum requirements, and the total cost is minimized.

### Approach
1. **Problem Analysis**: The problem involves selecting optimal quantities of various foods to minimize total cost subject to:
   - **Nutritional Constraints**:
     - For nutrients with minimum requirements, the total intake must be at least the specified minimum.
     - For nutrients with maximum requirements, the total intake must not exceed the specified maximum.
   - **Food Constraints**: Each food item has a minimum and maximum purchase amount.

2. **Formulation**:
   - **Objective Function**: Minimize the total cost, which is the sum of the cost of each food multiplied by the amount purchased.
   - **Constraints**:
     - Convert minimum nutrient requirements (≥) into ≤ constraints by multiplying by -1.
     - Keep maximum nutrient requirements as ≤ constraints.
     - Ensure the purchase amount of each food lies within its specified bounds.

3. **Solution Method**: 
   - Use the `linprog` function from the `scipy.optimize` module.
   - The 'highs' method is chosen for its efficiency in solving large-scale LP problems.

4. **Edge Cases**:
   - If there are no nutritional constraints, the solution is derived solely from food bounds.
   - If the problem is infeasible, return `None`.

### Solution Code
```python
from scipy.optimize import linprog

def dietu_problem(cost, f_min, f_max, n_min, n_max, amt):
    n_foods = len(cost)
    
    if len(f_min) != n_foods or len(f_max) != n_foods:
        raise ValueError("Length of f_min and f_max must match the number of foods")
    
    for j in range(n_foods):
        if f_min[j] < 0:
            raise ValueError("Food minimum amounts must be non-negative")
        if f_min[j] > f_max[j]:
            raise ValueError("Food minimum amount cannot exceed maximum amount")
    
    M = len(n_min)
    N = len(n_max)
    total_nutrients = M + N
    
    if total_nutrients > 0:
        if len(amt) != total_nutrients:
            raise ValueError(f"amt must have exactly {total_nutrients} rows (M+N), but got {len(amt)}")
        for i in range(total_nutrients):
            if len(amt[i]) != n_foods:
                raise ValueError(f"Row {i} of amt has {len(amt[i])} elements, expected {n_foods} (number of foods)")
    
    for i in range(M):
        if n_min[i] < 0:
            raise ValueError("Minimum nutrient requirements must be non-negative")
    for i in range(N):
        if n_max[i] < 0:
            raise ValueError("Maximum nutrient requirements must be non-negative")
    
    for i in range(total_nutrients):
        for j in range(n_foods):
            if amt[i][j] < 0:
                raise ValueError("Nutrient amounts must be non-negative")
    
    if total_nutrients == 0:
        A_ub = None
        b_ub = None
    else:
        A_ub = []
        b_ub = []
        for i in range(M):
            A_ub.append([-x for x in amt[i]])
            b_ub.append(-n_min[i])
        for i in range(M, total_nutrients):
            A_ub.append(amt[i])
            b_ub.append(n_max[i - M])
    
    bounds = [(f_min[j], f_max[j]) for j in range(n_foods)]
    
    res = linprog(cost, A_ub=A_ub, b_ub=b_ub, bounds=bounds, method='highs')
    
    if res.success:
        return res.fun
    else:
        return None
```

### Explanation
1. **Input Validation**: The function first checks if the dimensions of the input lists are consistent. It ensures that:
   - The lengths of `f_min` and `f_max` match the number of foods.
   - Each food's minimum amount is non-negative and does not exceed its maximum amount.
   - The nutrient amount matrix `amt` has the correct dimensions (number of nutrients × number of foods).
   - All nutrient requirements and amounts are non-negative.

2. **Constraint Setup**: 
   - For nutrients with minimum requirements, the constraints are converted from `≥` to `≤` by negating the coefficients and the right-hand side values.
   - For nutrients with maximum requirements, the constraints are directly used as `≤` inequalities.

3. **Bounds Handling**: Each food item's minimum and maximum purchase amounts are set as bounds for the decision variables.

4. **Linear Programming Solution**: The `linprog` function is called with the objective function (cost vector), inequality constraints, and variable bounds. The 'highs' method is efficient for large-scale problems.

5. **Result Handling**: If the solver finds an optimal solution, the minimal total cost is returned. If the problem is infeasible, `None` is returned.

This approach efficiently models and solves the diet problem using linear programming, ensuring optimal cost while meeting all nutritional and purchase constraints.