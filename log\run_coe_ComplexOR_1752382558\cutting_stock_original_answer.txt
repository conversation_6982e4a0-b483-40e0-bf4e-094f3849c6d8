To solve the Cutting Stock Problem, we need to minimize the total number of raw rolls used while meeting all customer orders for various widths. The solution involves using integer linear programming (ILP) to model the problem and solve it efficiently.

### Approach
1. **Initial Checks**: If all orders are zero, return 0 immediately since no rolls are needed.
2. **Pattern Validation**: For each pattern, calculate the total width it consumes. A pattern is valid if this total is positive and does not exceed the raw roll width.
3. **Infeasibility Check**: If there are no valid patterns and any order is positive, return -1 as the problem is infeasible.
4. **Integer Linear Programming Model**:
   - **Variables**: Integer variables representing the number of times each valid pattern is used.
   - **Objective**: Minimize the sum of these variables (total raw rolls used).
   - **Constraints**: For each width with positive orders, ensure the total rolls produced by all patterns meet or exceed the demand.
5. **Solution Extraction**: Solve the ILP model. If an optimal solution is found, return the total number of raw rolls; otherwise, return -1 indicating infeasibility.

### Solution Code
```python
import gurobipy as grb

def cutting_stock(roll_width, widths, orders, num_patterns, num_rolls_width):
    n = len(widths)
    
    if all(o == 0 for o in orders):
        return 0
        
    valid_patterns = [
        j for j in range(num_patterns)
        if 0 < sum(widths[i] * num_rolls_width[i][j] for i in range(n)) <= roll_width
    ]
    
    if not valid_patterns:
        return -1
        
    with grb.Model() as model:
        model.setParam('OutputFlag', 0)
        
        x = [
            model.addVar(vtype=grb.GRB.INTEGER, lb=0, name=f"x_{j}")
            for j in valid_patterns
        ]
        
        model.setObjective(grb.quicksum(x), grb.GRB.MINIMIZE)
        
        for i in range(n):
            if orders[i] > 0:
                model.addConstr(
                    grb.quicksum(
                        num_rolls_width[i][j] * x[k]
                        for k, j in enumerate(valid_patterns)
                    ) >= orders[i],
                    name=f"Demand_width_{i}"
                )
        
        model.optimize()
        
        if model.status == grb.GRB.Status.OPTIMAL:
            return int(model.objVal)
        return -1
```

### Explanation
1. **Initial Checks**: The function first checks if all orders are zero, returning 0 immediately to avoid unnecessary computation.
2. **Pattern Validation**: Each pattern is validated by ensuring the total width of its cuts is positive and does not exceed the raw roll width. Invalid patterns are discarded.
3. **Infeasibility Handling**: If no valid patterns exist and there are positive orders, the problem is infeasible, returning -1.
4. **ILP Model Setup**: 
   - **Variables**: Integer variables are created for each valid pattern, representing how many times each pattern is used.
   - **Objective**: The objective is to minimize the sum of these variables, which corresponds to the total number of raw rolls used.
   - **Constraints**: For each width with positive demand, a constraint ensures the total rolls produced by all patterns meet or exceed the order quantity.
5. **Solution Extraction**: The model is solved. If an optimal solution is found, the total number of raw rolls (objective value) is returned; otherwise, -1 is returned indicating no feasible solution exists.

This approach efficiently addresses the Cutting Stock Problem by leveraging integer programming to minimize raw material usage while meeting all demand constraints.