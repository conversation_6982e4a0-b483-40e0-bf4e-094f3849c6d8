import numpy as np
from scipy.optimize import linprog

def blend_problem(alloys_on_market, required_elements, composition_data, desired_blend_percentage, alloy_price):
    n = len(required_elements)
    m = len(alloys_on_market)
    
    if len(desired_blend_percentage) != n:
        raise ValueError("desired_blend_percentage must have the same length as required_elements")
    if len(alloy_price) != m:
        raise ValueError("alloy_price must have the same length as alloys_on_market")
    if len(composition_data) != n:
        raise ValueError("composition_data must have the same number of rows as required_elements")
    for i in range(n):
        if len(composition_data[i]) != m:
            raise ValueError(f"Row {i} of composition_data has length {len(composition_data[i])}, expected {m}")

    if any(p < 0 for p in alloy_price):
        raise ValueError("Alloy prices must be non-negative")
    for i in range(n):
        if any(comp < 0 for comp in composition_data[i]):
            raise ValueError(f"Composition percentages in row {i} must be non-negative")
    if any(d < 0 for d in desired_blend_percentage):
        raise ValueError("Desired blend percentages must be non-negative")

    if m == 0:
        return float('inf')

    comp_array = np.array(composition_data, dtype=float)
    A_eq = np.vstack((comp_array, np.ones(m)))
    b_eq = np.append(desired_blend_percentage, 1.0)
    bounds = [(0, None)] * m

    try:
        res = linprog(c=alloy_price, A_eq=A_eq, b_eq=b_eq, bounds=bounds, method='highs')
    except:
        return float('inf')
    
    if res.success:
        return res.fun
    else:
        return float('inf')