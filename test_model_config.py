#!/usr/bin/env python3
"""
Test script to verify that the model configuration is working correctly.
This script tests the create_chat_openai function with different model names.
"""

from utils import create_chat_openai

def test_model_configurations():
    """Test different model configurations"""
    
    print("Testing model configurations...")
    
    # Test o3-mini configuration
    print("\n1. Testing openai/o3-mini configuration:")
    try:
        llm_o3 = create_chat_openai('openai/o3-mini', temperature=0)
        print(f"✓ Successfully created ChatOpenAI instance for openai/o3-mini")
        print(f"  Model name: {llm_o3.model_name}")
        print(f"  API base: {llm_o3.openai_api_base}")
        print(f"  API key (first 10 chars): {llm_o3.openai_api_key[:10]}...")
    except Exception as e:
        print(f"✗ Error creating o3-mini instance: {e}")
    
    # Test o3-mini without prefix
    print("\n2. Testing o3-mini configuration:")
    try:
        llm_o3_short = create_chat_openai('o3-mini', temperature=0)
        print(f"✓ Successfully created ChatOpenAI instance for o3-mini")
        print(f"  Model name: {llm_o3_short.model_name}")
        print(f"  API base: {llm_o3_short.openai_api_base}")
        print(f"  API key (first 10 chars): {llm_o3_short.openai_api_key[:10]}...")
    except Exception as e:
        print(f"✗ Error creating o3-mini instance: {e}")
    
    # Test DeepSeek configuration
    print("\n3. Testing deepseek-reasoner configuration:")
    try:
        llm_deepseek = create_chat_openai('deepseek-reasoner', temperature=0)
        print(f"✓ Successfully created ChatOpenAI instance for deepseek-reasoner")
        print(f"  Model name: {llm_deepseek.model_name}")
        print(f"  API base: {llm_deepseek.openai_api_base}")
        print(f"  API key (first 10 chars): {llm_deepseek.openai_api_key[:10]}...")
    except Exception as e:
        print(f"✗ Error creating deepseek instance: {e}")
    
    # Test default configuration (None)
    print("\n4. Testing default configuration (None):")
    try:
        llm_default = create_chat_openai(None, temperature=0)
        print(f"✓ Successfully created ChatOpenAI instance for default (None)")
        print(f"  Model name: {llm_default.model_name}")
        print(f"  API base: {llm_default.openai_api_base}")
        print(f"  API key (first 10 chars): {llm_default.openai_api_key[:10]}...")
    except Exception as e:
        print(f"✗ Error creating default instance: {e}")
    
    # Test standard OpenAI configuration
    print("\n5. Testing standard OpenAI configuration (gpt-3.5-turbo):")
    try:
        llm_openai = create_chat_openai('gpt-3.5-turbo', temperature=0)
        print(f"✓ Successfully created ChatOpenAI instance for gpt-3.5-turbo")
        print(f"  Model name: {llm_openai.model_name}")
        print(f"  API base: {getattr(llm_openai, 'openai_api_base', 'Default OpenAI')}")
        print(f"  Uses environment OPENAI_API_KEY: {not hasattr(llm_openai, 'openai_api_key')}")
    except Exception as e:
        print(f"✗ Error creating OpenAI instance: {e}")

if __name__ == "__main__":
    test_model_configurations()
    print("\n" + "="*50)
    print("Configuration test completed!")
    print("The default model is now set to: openai/o3-mini")
    print("API Key: 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")
    print("API Base: https://openrouter.ai/api/v1")
