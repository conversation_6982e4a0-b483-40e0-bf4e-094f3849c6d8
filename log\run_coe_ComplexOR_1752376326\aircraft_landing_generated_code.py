from pulp import *

def aircraft_landing(EarliestLanding, LatestLanding, TargetLanding, PenaltyAfterTarget, PenaltyBeforeTarget, SeparationTime):
    n = len(EarliestLanding)
    
    # Input validation
    for i in range(n):
        E = EarliestLanding[i]
        L = LatestLanding[i]
        T = TargetLanding[i]
        if not (E <= T <= L):
            return -1
        if PenaltyBeforeTarget[i] < 0 or PenaltyAfterTarget[i] < 0:
            return -1
    
    for i in range(n):
        for j in range(n):
            if SeparationTime[i][j] < 0:
                return -1
                
    prob = LpProblem("Aircraft_Landing_Problem", LpMinimize)
    
    t = [LpVariable(f"t_{i}", lowBound=EarliestLanding[i], upBound=LatestLanding[i]) for i in range(n)]
    alpha = [LpVariable(f"alpha_{i}", lowBound=0) for i in range(n)]
    beta = [LpVariable(f"beta_{i}", lowBound=0) for i in range(n)]
    
    total_penalty = lpSum([PenaltyBeforeTarget[i] * alpha[i] + PenaltyAfterTarget[i] * beta[i] for i in range(n)])
    prob += total_penalty
    
    for i in range(n):
        prob += t[i] + alpha[i] - beta[i] == TargetLanding[i], f"TargetTime_{i}"
    
    for i in range(n):
        for j in range(i + 1, n):
            prob += t[j] >= t[i] + SeparationTime[i][j], f"Separation_{i}_{j}"
    
    status = prob.solve(PULP_CBC_CMD(msg=False))
    
    if LpStatus[status] == "Optimal":
        total_penalty_value = value(total_penalty)
        return int(round(total_penalty_value))
    else:
        return -1