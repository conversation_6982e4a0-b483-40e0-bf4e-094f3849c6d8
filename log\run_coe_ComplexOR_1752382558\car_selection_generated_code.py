import gurobipy as gp
from gurobipy import GRB

def car_selection(participants, cars, possible_assignments):
    n = len(participants)
    m = len(cars)
    
    if len(possible_assignments) != n:
        raise ValueError("Participants dimension mismatch in possible_assignments")
    if any(len(row) != m for row in possible_assignments):
        raise ValueError("Cars dimension mismatch in possible_assignments")
    
    model = gp.Model("CarSelection")
    
    indices = [
        (p, c) 
        for p in range(n) 
        for c in range(m) 
        if possible_assignments[p][c] == 1
    ]
    x = model.addVars(indices, vtype=GRB.BINARY, name="x")
    
    model.setObjective(x.sum(), GRB.MAXIMIZE)
    
    participant_assignments = {}
    for (p, c) in indices:
        participant_assignments.setdefault(p, []).append(x[p, c])
    
    for p, vars_list in participant_assignments.items():
        model.addConstr(gp.quicksum(vars_list) <= 1, name=f"Part_{p}_limit")
    
    model.setParam('OutputFlag', 0)
    model.optimize()
    
    if model.status == GRB.OPTIMAL:
        return int(model.objVal)
    elif model.status == GRB.INFEASIBLE:
        raise RuntimeError("Model is infeasible with given constraints")
    elif model.status == GRB.TIME_LIMIT:
        raise RuntimeError("Solver reached time limit before finding optimal solution")
    else:
        raise RuntimeError(f"Solver failed with status: {model.status}")