To solve the alloy blending problem, we formulate it as a linear programming (LP) problem where the objective is to minimize the total cost while meeting elemental composition requirements and total blend constraints. The solution uses the `linprog` function from `scipy.optimize` to efficiently find the optimal amounts of each alloy to purchase.

### Approach
1. **Problem Analysis**: The goal is to blend alloys such that the resulting mixture meets specified elemental percentages at minimum cost. The total blend quantity must be exactly 1 unit.
2. **LP Formulation**:
   - **Decision Variables**: Amount of each alloy to purchase (continuous, non-negative).
   - **Objective**: Minimize total cost (sum of alloy costs).
   - **Constraints**:
     - Elemental composition: For each element, the weighted sum of its percentages from all alloys equals the desired blend percentage.
     - Total blend: Sum of all alloy amounts equals 1.
3. **Solution Method**: Use `linprog` with equality constraints for elemental and blend requirements, and non-negativity bounds.

### Solution Code
```python
from scipy.optimize import linprog

def blend_problem(alloys_on_market, required_elements, composition_data, desired_blend_percentage, alloy_price):
    n = len(alloys_on_market)
    m = len(required_elements)
    
    # Validate input dimensions
    assert len(composition_data) == n, "Mismatched alloys in composition_data"
    for i in range(n):
        assert len(composition_data[i]) == m, f"Mismatched elements for alloy {i}"
    assert len(desired_blend_percentage) == m, "Mismatched blend percentages"
    assert len(alloy_price) == n, "Mismatched alloy prices"
    
    # Build constraint matrix (A_eq) and vector (b_eq)
    A_eq = []
    for j in range(m):
        A_eq.append([composition_data[i][j] for i in range(n)])
    A_eq.append([1.0] * n)  # Total blend constraint
    b_eq = list(desired_blend_percentage) + [1.0]  # Includes blend percentages and total amount
    
    # Solve linear program
    res = linprog(
        c=alloy_price,
        A_eq=A_eq,
        b_eq=b_eq,
        bounds=[(0, None)] * n,
        method='highs',
        tol=1e-8
    )
    
    return res.fun if res.success else float('nan')
```

### Explanation
1. **Input Validation**: Checks ensure dimensions of inputs (alloys, elements, composition data, blend percentages, prices) are consistent.
2. **Constraint Setup**:
   - `A_eq` matrix rows represent elemental composition constraints (one per element) followed by the total blend constraint (all 1s).
   - `b_eq` vector contains desired elemental percentages and the total blend amount (1.0).
3. **LP Solution**: The `linprog` function minimizes total cost (`alloy_price`) subject to:
   - Equality constraints (`A_eq` and `b_eq`) for composition and blend requirements.
   - Non-negativity bounds on alloy amounts.
4. **Result Handling**: Returns minimum cost if successful; otherwise returns `NaN` indicating no feasible solution.

This approach efficiently models the blending problem using linear programming and leverages optimized solvers to find the cost-minimal solution.