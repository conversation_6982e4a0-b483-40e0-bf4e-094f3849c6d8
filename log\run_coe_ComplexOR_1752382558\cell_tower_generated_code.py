import gurobipy as gp
from gurobipy import GRB

def cell_tower(delta, cost, population, budget):
    num_towers = len(delta)
    num_regions = len(population)
    
    coverage_map = [
        [i for i in range(num_towers) if delta[i][j] == 1]
        for j in range(num_regions)
    ]
    
    try:
        with gp.Model("CellTower") as model:
            model.setParam('OutputFlag', 0)
            
            x = model.addVars(num_towers, vtype=GRB.BINARY, name="x")
            y = model.addVars(num_regions, vtype=GRB.CONTINUOUS, lb=0, ub=1, name="y")
            
            model.addConstr(
                gp.quicksum(cost[i] * x[i] for i in range(num_towers)) <= budget,
                "Budget"
            )
            
            for j, towers in enumerate(coverage_map):
                model.addConstr(
                    y[j] <= gp.quicksum(x[i] for i in towers),
                    f"Coverage_Region_{j}"
                )
            
            model.setObjective(
                gp.quicksum(population[j] * y[j] for j in range(num_regions)),
                GRB.MAXIMIZE
            )
            
            model.optimize()
            
            if model.status == GRB.OPTIMAL:
                return int(round(model.objVal))
            return 0
            
    except gp.GurobiError as e:
        return 0
    except Exception as e:
        return 0