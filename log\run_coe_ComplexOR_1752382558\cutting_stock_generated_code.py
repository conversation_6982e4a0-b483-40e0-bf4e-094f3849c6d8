import gurobipy as grb

def cutting_stock(roll_width, widths, orders, num_patterns, num_rolls_width):
    n = len(widths)
    
    if all(o == 0 for o in orders):
        return 0
        
    valid_patterns = [
        j for j in range(num_patterns)
        if 0 < sum(widths[i] * num_rolls_width[i][j] for i in range(n)) <= roll_width
    ]
    
    if not valid_patterns:
        return -1
        
    with grb.Model() as model:
        model.setParam('OutputFlag', 0)
        
        x = [
            model.addVar(vtype=grb.GRB.INTEGER, lb=0, name=f"x_{j}")
            for j in valid_patterns
        ]
        
        model.setObjective(grb.quicksum(x), grb.GRB.MINIMIZE)
        
        for i in range(n):
            if orders[i] > 0:
                model.addConstr(
                    grb.quicksum(
                        num_rolls_width[i][j] * x[k]
                        for k, j in enumerate(valid_patterns)
                    ) >= orders[i],
                    name=f"Demand_width_{i}"
                )
        
        model.optimize()
        
        if model.status == grb.GRB.Status.OPTIMAL:
            return int(model.objVal)
        return -1