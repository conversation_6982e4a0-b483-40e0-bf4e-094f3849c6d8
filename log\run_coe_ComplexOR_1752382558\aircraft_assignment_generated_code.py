import pulp

def aircraft_assignment(availability, demand, capabilities, costs):
    # Validate inputs
    if any(avail < 0 for avail in availability):
        raise ValueError("Aircraft availability must be non-negative")
    if any(d < 0 for d in demand):
        raise ValueError("Route demand must be non-negative")
    for i in range(len(availability)):
        for j in range(len(demand)):
            if capabilities[i][j] < 0:
                raise ValueError("Capabilities must be non-negative")
            if costs[i][j] < 0:
                raise ValueError("Costs must be non-negative")
    
    n_aircraft = len(availability)
    n_routes = len(demand)
    aircraft_indices = range(n_aircraft)
    route_indices = range(n_routes)
    
    prob = pulp.LpProblem("Aircraft_Assignment_Problem", pulp.LpMinimize)
    
    x = pulp.LpVariable.dicts("x", 
                              ((i, j) for i in aircraft_indices for j in route_indices),
                              lowBound=0,
                              cat='Integer')
    
    prob += pulp.lpSum(costs[i][j] * x[i, j] for i in aircraft_indices for j in route_indices)
    
    for j in route_indices:
        prob += pulp.lpSum(capabilities[i][j] * x[i, j] for i in aircraft_indices) >= demand[j], f"Route_{j}_Demand"
    
    for i in aircraft_indices:
        prob += pulp.lpSum(x[i, j] for j in route_indices) <= availability[i], f"Aircraft_{i}_Availability"
    
    solver = pulp.PULP_CBC_CMD(msg=False)
    status = prob.solve(solver)
    
    if pulp.LpStatus[status] == 'Optimal':
        min_total_cost = pulp.value(prob.objective)
        return min_total_cost
    else:
        return None