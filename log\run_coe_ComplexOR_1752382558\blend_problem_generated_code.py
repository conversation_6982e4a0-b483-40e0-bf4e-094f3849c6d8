from scipy.optimize import linprog

def blend_problem(alloys_on_market, required_elements, composition_data, desired_blend_percentage, alloy_price):
    n = len(alloys_on_market)
    m = len(required_elements)
    
    # Validate input dimensions
    assert len(composition_data) == n, "Mismatched alloys in composition_data"
    for i in range(n):
        assert len(composition_data[i]) == m, f"Mismatched elements for alloy {i}"
    assert len(desired_blend_percentage) == m, "Mismatched blend percentages"
    assert len(alloy_price) == n, "Mismatched alloy prices"
    
    # Build constraint matrix (A_eq) and vector (b_eq)
    A_eq = []
    for j in range(m):
        A_eq.append([composition_data[i][j] for i in range(n)])
    A_eq.append([1.0] * n)  # Total blend constraint
    b_eq = list(desired_blend_percentage) + [1.0]  # Includes blend percentages and total amount
    
    # Solve linear program
    res = linprog(
        c=alloy_price,
        A_eq=A_eq,
        b_eq=b_eq,
        bounds=[(0, None)] * n,
        method='highs',
        tol=1e-8
    )
    
    return res.fun if res.success else float('nan')