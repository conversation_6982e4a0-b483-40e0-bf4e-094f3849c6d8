import pulp

def aircraft_landing(EarliestLanding, LatestLanding, TargetLanding, PenaltyAfterTarget, PenaltyBeforeTarget, SeparationTime):
    n = len(EarliestLanding)
    min_E = min(EarliestLanding)
    max_L = max(LatestLanding)
    max_sep = 0
    for i in range(n):
        for j in range(n):
            if SeparationTime[i][j] > max_sep:
                max_sep = SeparationTime[i][j]
    M = max_L - min_E + max_sep

    prob = pulp.LpProblem("Aircraft_Landing_Problem", pulp.LpMinimize)
    
    t = [pulp.LpVariable(f"t_{i}", lowBound=EarliestLanding[i], upBound=LatestLanding[i]) for i in range(n)]
    b = [pulp.LpVariable(f"b_{i}", lowBound=0) for i in range(n)]
    a = [pulp.LpVariable(f"a_{i}", lowBound=0) for i in range(n)]
    
    x = {}
    for i in range(n):
        for j in range(i+1, n):
            x[(i, j)] = pulp.LpVariable(f"x_{i}_{j}", cat='Binary')
    
    for i in range(n):
        prob += t[i] + b[i] - a[i] == TargetLanding[i], f"target_deviation_{i}"
    
    for i in range(n):
        for j in range(i+1, n):
            prob += t[j] >= t[i] + SeparationTime[i][j] - M * (1 - x[(i, j)]), f"sep_{i}_{j}_i_before_j"
            prob += t[i] >= t[j] + SeparationTime[j][i] - M * x[(i, j)], f"sep_{i}_{j}_j_before_i"
    
    total_penalty = pulp.lpSum([PenaltyBeforeTarget[i] * b[i] for i in range(n)]) + pulp.lpSum([PenaltyAfterTarget[i] * a[i] for i in range(n)])
    prob += total_penalty
    
    prob.solve(pulp.PULP_CBC_CMD(msg=False))
    
    if pulp.LpStatus[prob.status] == 'Optimal':
        total_penalty_value = pulp.value(total_penalty)
        min_total_penalty = int(round(total_penalty_value))
    else:
        min_total_penalty = 10**18
    
    return min_total_penalty