To solve the Aircraft Landing Problem (ALP), we need to schedule aircraft landings such that each aircraft lands within its specified time window, adheres to separation constraints between successive landings, and minimizes the total penalty for deviations from target landing times. The solution involves formulating the problem as a Mixed Integer Linear Program (MILP) and solving it using the PuLP library with the CBC solver.

### Approach
1. **Problem Analysis**: The problem involves scheduling aircraft landings with constraints on time windows and separation times between aircraft. The objective is to minimize the total penalty incurred by landing before or after the target landing times.
2. **MILP Formulation**:
   - **Variables**:
     - `t_i`: Continuous variable representing the landing time of aircraft `i`.
     - `b_i`: Continuous non-negative variable representing the time by which aircraft `i` lands before its target time.
     - `a_i`: Continuous non-negative variable representing the time by which aircraft `i` lands after its target time.
     - `x_ij`: Binary variable for each pair `(i, j)` (where `i < j`), which is 1 if aircraft `i` lands before `j`, and 0 otherwise.
   - **Constraints**:
     - **Target Deviation**: For each aircraft `i`, the landing time plus the earliness minus the tardiness equals the target landing time.
     - **Time Window**: Each aircraft must land within its earliest and latest landing times.
     - **Separation**: For each pair of aircraft `(i, j)`, if `i` lands before `j`, then `j`'s landing time must be at least `i`'s landing time plus the separation time `s_ij`. Similarly, if `j` lands before `i`, then `i`'s landing time must be at least `j`'s landing time plus the separation time `s_ji`.
   - **Objective**: Minimize the total penalty, which is the sum of penalties for earliness and tardiness across all aircraft.
3. **Big-M Method**: A sufficiently large constant `M` is used to deactivate separation constraints when they are not relevant, ensuring the model remains linear.
4. **Solution**: The MILP is solved using the PuLP library with the CBC solver. The solution is rounded to the nearest integer to meet the requirement of returning an integer total penalty.

### Solution Code
```python
import pulp

def aircraft_landing(EarliestLanding, LatestLanding, TargetLanding, PenaltyAfterTarget, PenaltyBeforeTarget, SeparationTime):
    n = len(EarliestLanding)
    min_E = min(EarliestLanding)
    max_L = max(LatestLanding)
    max_sep = 0
    for i in range(n):
        for j in range(n):
            if SeparationTime[i][j] > max_sep:
                max_sep = SeparationTime[i][j]
    M = max_L - min_E + max_sep

    prob = pulp.LpProblem("Aircraft_Landing_Problem", pulp.LpMinimize)
    
    t = [pulp.LpVariable(f"t_{i}", lowBound=EarliestLanding[i], upBound=LatestLanding[i]) for i in range(n)]
    b = [pulp.LpVariable(f"b_{i}", lowBound=0) for i in range(n)]
    a = [pulp.LpVariable(f"a_{i}", lowBound=0) for i in range(n)]
    
    x = {}
    for i in range(n):
        for j in range(i+1, n):
            x[(i, j)] = pulp.LpVariable(f"x_{i}_{j}", cat='Binary')
    
    for i in range(n):
        prob += t[i] + b[i] - a[i] == TargetLanding[i], f"target_deviation_{i}"
    
    for i in range(n):
        for j in range(i+1, n):
            prob += t[j] >= t[i] + SeparationTime[i][j] - M * (1 - x[(i, j)]), f"sep_{i}_{j}_i_before_j"
            prob += t[i] >= t[j] + SeparationTime[j][i] - M * x[(i, j)], f"sep_{i}_{j}_j_before_i"
    
    total_penalty = pulp.lpSum([PenaltyBeforeTarget[i] * b[i] for i in range(n)]) + pulp.lpSum([PenaltyAfterTarget[i] * a[i] for i in range(n)])
    prob += total_penalty
    
    prob.solve(pulp.PULP_CBC_CMD(msg=False))
    
    if pulp.LpStatus[prob.status] == 'Optimal':
        total_penalty_value = pulp.value(total_penalty)
        min_total_penalty = int(round(total_penalty_value))
    else:
        min_total_penalty = 10**18
    
    return min_total_penalty
```

### Explanation
1. **Initialization**: The function starts by determining the number of aircraft `n` and calculating the big-M constant `M` using the earliest and latest landing times and the maximum separation time.
2. **Problem Setup**: A MILP problem is initialized with the objective to minimize the total penalty.
3. **Variables**:
   - `t_i` represents the landing time for each aircraft, bounded by its earliest and latest landing times.
   - `b_i` and `a_i` capture the earliness and tardiness deviations from the target landing time.
   - `x_ij` is a binary variable for each aircraft pair `(i, j)` to indicate the landing order.
4. **Constraints**:
   - **Target Deviation**: Ensures the sum of landing time, earliness, and tardiness equals the target time for each aircraft.
   - **Separation**: Uses the big-M method to enforce separation times based on the landing order determined by `x_ij`.
5. **Objective**: The total penalty is computed as the sum of penalties for earliness and tardiness across all aircraft.
6. **Solution**: The problem is solved using the CBC solver. If an optimal solution is found, the total penalty is rounded to the nearest integer; otherwise, a large value (indicating infeasibility) is returned.

This approach efficiently schedules aircraft landings while minimizing penalties and adhering to all constraints, leveraging MILP for optimal solutions.