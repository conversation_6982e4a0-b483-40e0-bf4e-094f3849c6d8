To solve the Aircraft Assignment Problem, we need to minimize the total cost of assigning aircraft to routes while satisfying passenger demand constraints and respecting aircraft availability limits. This problem is formulated as an Integer Linear Program (ILP) using the PuLP library, which efficiently handles such optimization problems.

### Approach
1. **Problem Formulation**: The problem is modeled as an ILP where:
   - **Decision Variables**: \( x_{ij} \) represents the number of aircraft of type \( i \) assigned to route \( j \).
   - **Objective Function**: Minimize the total cost \( \sum_{i,j} \text{costs}_{ij} \times x_{ij} \).
   - **Constraints**:
     - **Demand Constraints**: For each route \( j \), the total passengers carried must meet or exceed the demand: \( \sum_{i} \text{capabilities}_{ij} \times x_{ij} \geq \text{demand}_j \).
     - **Availability Constraints**: For each aircraft type \( i \), the total assignments must not exceed available aircraft: \( \sum_{j} x_{ij} \leq \text{availability}_i \).
     - **Non-negativity and Integer Constraints**: \( x_{ij} \) must be non-negative integers.

2. **Input Validation**: Ensure all input parameters (availability, demand, capabilities, costs) are non-negative to prevent invalid solutions.

3. **Solver Selection**: The CBC solver via PuLP is used to solve the ILP efficiently.

4. **Solution Extraction**: After solving, if an optimal solution is found, return the total cost; otherwise, return `None`.

### Solution Code
```python
import pulp

def aircraft_assignment(availability, demand, capabilities, costs):
    # Validate inputs
    if any(avail < 0 for avail in availability):
        raise ValueError("Aircraft availability must be non-negative")
    if any(d < 0 for d in demand):
        raise ValueError("Route demand must be non-negative")
    for i in range(len(availability)):
        for j in range(len(demand)):
            if capabilities[i][j] < 0:
                raise ValueError("Capabilities must be non-negative")
            if costs[i][j] < 0:
                raise ValueError("Costs must be non-negative")
    
    n_aircraft = len(availability)
    n_routes = len(demand)
    aircraft_indices = range(n_aircraft)
    route_indices = range(n_routes)
    
    prob = pulp.LpProblem("Aircraft_Assignment_Problem", pulp.LpMinimize)
    
    x = pulp.LpVariable.dicts("x", 
                              ((i, j) for i in aircraft_indices for j in route_indices),
                              lowBound=0,
                              cat='Integer')
    
    prob += pulp.lpSum(costs[i][j] * x[i, j] for i in aircraft_indices for j in route_indices)
    
    for j in route_indices:
        prob += pulp.lpSum(capabilities[i][j] * x[i, j] for i in aircraft_indices) >= demand[j], f"Route_{j}_Demand"
    
    for i in aircraft_indices:
        prob += pulp.lpSum(x[i, j] for j in route_indices) <= availability[i], f"Aircraft_{i}_Availability"
    
    solver = pulp.PULP_CBC_CMD(msg=False)
    status = prob.solve(solver)
    
    if pulp.LpStatus[status] == 'Optimal':
        min_total_cost = pulp.value(prob.objective)
        return min_total_cost
    else:
        return None
```

### Explanation
1. **Input Validation**: The function first checks if all input parameters (availability, demand, capabilities, costs) are non-negative. If any parameter is negative, a `ValueError` is raised.
2. **Problem Initialization**: The problem is initialized using `pulp.LpProblem` with the goal of minimizing the total cost.
3. **Decision Variables**: Variables \( x_{ij} \) are created for each aircraft-route pair as non-negative integers.
4. **Objective Function**: The total cost is defined as the sum of individual assignment costs multiplied by the number of aircraft assigned.
5. **Constraints**:
   - **Demand Constraints**: Ensure the total capacity assigned to each route meets or exceeds its demand.
   - **Availability Constraints**: Ensure the number of aircraft used does not exceed available quantities.
6. **Solving the Problem**: The CBC solver is used to find the optimal solution. If an optimal solution is found, the total cost is returned; otherwise, `None` is returned.

This approach efficiently models and solves the Aircraft Assignment Problem, ensuring optimal resource utilization while meeting all constraints. The solution is robust, handling invalid inputs gracefully and providing the minimal total cost for valid scenarios.